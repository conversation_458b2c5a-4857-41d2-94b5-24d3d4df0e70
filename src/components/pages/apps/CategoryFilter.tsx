'use client';

import { useCallback, useRef, useEffect, useState } from 'react';
import { tv } from 'tailwind-variants';
import { CategoryPill } from './CategoryPill';

const categoryFilterVariants = tv({
  slots: {
    wrapper:
      "w-full bg-background-blue pt-4 transition-all duration-200 ease-in-out border-t border-gray-500 before:content-[''] before:absolute before:-top-2 before:left-0 before:right-0 before:h-2 before:bg-background-blue",
    scrollContainer:
      'mt-0 pt-2 leading-none flex gap-2 hht:gap-3 tablet:gap-4 overflow-x-auto overflow-y-hidden px-4 hht:px-4 tablet:px-6 scrollbar-hide bg-background-blue transition-all duration-200 ease-in-out',
  },
  variants: {
    isSticky: {
      true: {
        scrollContainer: 'pb-6',
      },
      false: {
        scrollContainer: 'pb-3',
      },
    },
  },
});

interface CategoryFilterProps {
  categories: string[];
  selectedCategory: string;
  onCategorySelect: (category: string) => void;
  className?: string;
}

export function CategoryFilter({
  categories,
  selectedCategory,
  onCategorySelect,
  className,
}: CategoryFilterProps) {
  const [focusedIndex, setFocusedIndex] = useState<number>(() => {
    return categories.findIndex((cat) => cat === selectedCategory);
  });
  const [isSticky, setIsSticky] = useState(false);
  const observerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = observerRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsSticky(entry.isIntersecting === false);
      },
      { threshold: [0], rootMargin: '-1px 0px 0px 0px' },
    );

    observer.observe(element);

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, []);

  const categoryRefs = useRef<(HTMLButtonElement | null)[]>([]);

  const handleCategorySelect = useCallback(
    (category: string) => {
      onCategorySelect(category);
    },
    [onCategorySelect],
  );

  useEffect(() => {
    const newIndex = categories.findIndex((cat) => cat === selectedCategory);
    if (newIndex !== -1) {
      setFocusedIndex(newIndex);
    }
  }, [categories, selectedCategory]);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          setFocusedIndex((prev) => {
            const newIndex = prev > 0 ? prev - 1 : categories.length - 1;
            categoryRefs.current[newIndex]?.focus();
            return newIndex;
          });
          break;
        case 'ArrowRight':
          event.preventDefault();
          setFocusedIndex((prev) => {
            const newIndex = prev < categories.length - 1 ? prev + 1 : 0;
            categoryRefs.current[newIndex]?.focus();
            return newIndex;
          });
          break;
        case 'Home':
          event.preventDefault();
          setFocusedIndex(0);
          categoryRefs.current[0]?.focus();
          break;
        case 'End':
          event.preventDefault();
          const lastIndex = categories.length - 1;
          setFocusedIndex(lastIndex);
          categoryRefs.current[lastIndex]?.focus();
          break;
        case 'Enter':
        case ' ':
          event.preventDefault();
          const category = categories[focusedIndex];
          if (category) {
            handleCategorySelect(category);
          }
          break;
      }
    },
    [categories, focusedIndex, handleCategorySelect],
  );

  const { wrapper, scrollContainer } = categoryFilterVariants({
    isSticky,
  });

  return (
    <>
      <div ref={observerRef} className="min-h-[1px]" />
      <div className={`${wrapper()} sticky top-0 z-10 ${className || ''}`}>
        <div
          className="w-full bg-background-blue pt-0"
          role="tablist"
          aria-label="Filter apps by category"
        >
          <div
            className={scrollContainer()}
            style={{
              scrollSnapType: 'x mandatory',
              WebkitOverflowScrolling: 'touch',
              scrollPaddingLeft: '16px',
              scrollPaddingRight: '16px',
              marginTop: '0px',
              paddingTop: '4px',
            }}
          >
            {categories.map((category, index) => (
              <CategoryPill
                key={category}
                ref={(el) => {
                  categoryRefs.current[index] = el;
                }}
                category={category}
                isSelected={selectedCategory === category}
                onClick={handleCategorySelect}
                className="flex-shrink-0"
                tabIndex={focusedIndex === index ? 0 : -1}
                onKeyDown={handleKeyDown}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
