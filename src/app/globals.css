@import 'tailwindcss';

@theme {
  --color-white: #ffffff;
  --color-foreground-light: #171717;
  --color-background-dark: #0a0a0a;
  --color-foreground-dark: #ededed;
  --color-background-blue: #eef3fc;
  --color-background-black: #282828;

  --breakpoint-hht: 360px;
  --breakpoint-tablet: 600px;
  --breakpoint-tablet-lg: 768px;
  --shadow-standard: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);
}

/* Hide scrollbars for horizontal scrolling containers */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Accessibility: Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Touch optimization for better mobile experience */
@media (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: transparent;
  }

  button,
  [role='button'],
  [role='tab'] {
    touch-action: manipulation;
  }
}
